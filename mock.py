from fastapi import FastAPI, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from typing import List
import uvicorn

app = FastAPI()

# Cho phép CORS cho frontend local
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/api/compare-invoice-contract/")
async def compare_invoice_contract(
    invoice: UploadFile = File(...),
    contract: UploadFile = File(...)
):
    # Fake response
    return {
        "preview": {
            "invoice": [
                {"InvoiceID": "INV001", "Amount": 1000, "Date": "2024-06-01"},
                {"InvoiceID": "INV002", "Amount": 2000, "Date": "2024-06-02"},
            ],
            "contract": [
                {"ContractID": "CON001", "Amount": 1000, "Date": "2024-06-01"},
                {"ContractID": "CON002", "Amount": 2500, "Date": "2024-06-02"},
            ]
        },
        "analysis": {
            "matched": [
                {"InvoiceID": "INV001", "ContractID": "CON001", "Amount": 1000, "Status": "Matched"},
            ],
            "unmatched": [
                {"InvoiceID": "INV002", "ContractID": "CON002", "InvoiceAmount": 2000, "ContractAmount": 2500, "Status": "Discrepancy"},
            ],
            "summary": {
                "total_invoices": 2,
                "total_contracts": 2,
                "matched": 1,
                "discrepancies": 1
            },
            "chart_data": {
                "labels": ["Matched", "Discrepancy"],
                "values": [1, 1]
            }
        }
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)